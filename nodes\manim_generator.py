import os
import uuid
import hashlib
import json
from typing import Dict, Any, List, Tuple
from langchain_core.messages import BaseMessage

from utils.file_utils import FileManager
from utils.prompt_utils import PromptManager
from utils.s3_utils import SVGSaveS3
from utils.db_utils import write_project_video_scene, write_project_video_scene_material
from utils.manim_utils import ManimUtils
from llm.claude_local import ClaudeLocalProvider
from llm.batch_processor import BatchProcessor
from utils.db_pool import DBConnectionPool

class ManimGenerator:
    """Manim代码生成节点"""

    def __init__(self, conf_path="../conf/conf.ini"):
        """
        初始化Manim代码生成节点

        Args:
            conf_path: 配置文件路径
        """
        self.file_manager = FileManager()
        self.prompt_manager = PromptManager()
        self.llm = ClaudeLocalProvider()
        self.batch_processor = BatchProcessor(provider=self.llm, max_concurrent=10)
        self.conf_path = conf_path

        # 初始化S3存储工具
        self.s3_handler = SVGSaveS3(conf_path)

        # 初始化数据库连接池
        self.db_pool = DBConnectionPool(conf_path)

        # 创建Manim输出目录
        self.manim_output_dir = os.path.join("data", "manim_output")
        os.makedirs(self.manim_output_dir, exist_ok=True)

        # 生成唯一的项目ID
        self.project_id = None

    async def process(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理数据

        Args:
            state: 当前状态

        Returns:
            Dict[str, Any]: 更新后的状态
        """
        print("\n" + "="*50)
        print("【开始执行】Manim代码生成 (ManimGenerator)")
        print("="*50)

        try:
            # 初始化项目信息和目录
            project_dir, project_hash = self._init_project_info(state)

            # 执行批处理生成Manim代码
            results = await self._generate_manim_batch(state, project_hash, project_dir)

            # 保存脚本文件
            data_state_path = self._save_script_file(state, project_dir, project_hash)

            # 更新状态和保存结果
            scene_paths = self._update_state_with_results(state, results)

            # 添加项目路径到状态中，供后续节点使用
            state["data"]["project_dir"] = project_dir
            state["data"]["data_state_path"] = data_state_path
            state["data"]["scene_paths"] = scene_paths

            state["current_step"] = "generate_manim"

            print("="*50)
            print("【完成执行】Manim代码生成 (ManimGenerator)")
            print("="*50 + "\n")

            return state
        finally:
            # 这里不需要显式关闭连接池，因为连接会自动返回到池中
            pass

    def _init_project_info(self, state: Dict[str, Any]) -> Tuple[str, str]:
        """
        初始化项目信息和目录

        Args:
            state: 当前状态

        Returns:
            Tuple[str, str]: 项目目录路径和项目哈希
        """
        # 获取热词和标题信息
        trend_word = state["data"].get("trend_word", "未知热词")
        title = state["data"].get("news_report", {}).get("title", "未知标题")
        print(f"热词: {trend_word}, 标题: {title}")

        # 生成唯一的项目标识符
        project_hash = hashlib.md5(f"{trend_word}".encode()).hexdigest()
        project_dir = f"manim_video/{project_hash}"
        print(f"项目目录: {project_dir}")

        # 创建项目目录
        os.makedirs(os.path.join(self.manim_output_dir, project_hash), exist_ok=True)

        return project_dir, project_hash

    async def _generate_manim_batch(self, state: Dict[str, Any], project_hash: str, project_dir: str) -> List[Tuple[str, str, str]]:
        """
        执行批处理生成Manim代码

        Args:
            state: 当前状态
            project_hash: 项目哈希
            project_dir: 项目目录

        Returns:
            List[Tuple[str, str, str]]: 生成结果列表，每项包含(场景ID, Manim代码, S3路径)
        """
        # 从news_report中获取SVG提示词（现在用于Manim代码生成）
        news_report = state["data"].get("news_report", {})
        sections = news_report.get("sections", [])

        # 准备批处理数据
        batch_items = []

        # 收集所有需要处理的场景和镜头
        for i, section in enumerate(sections):
            section_id = f"section_{i+1}"

            # 获取当前场景的子镜头列表
            subsections = section.get("subsections", [])

            if subsections:
                # 如果有子镜头，则处理每个子镜头的svg_prompt
                for j, subsection in enumerate(subsections):
                    shot_id = f"{section_id}_shot_{j+1}"
                    svg_prompt = subsection.get("svg_prompt")
                    if svg_prompt:
                        batch_items.append((shot_id, svg_prompt))
                        print(f"准备处理场景 {section_id} 的镜头 {j+1} 的Manim代码生成")
            else:
                # 如果没有子镜头，则处理整个场景的svg_prompt
                svg_prompt = section.get("svg_prompt")
                if svg_prompt:
                    batch_items.append((section_id, svg_prompt))
                    print(f"准备处理场景 {i+1} 的Manim代码生成")

        print(f"准备批处理 {len(batch_items)} 个场景的Manim代码生成")

        # 定义一个异步的包装函数来处理响应
        async def process_manim_response_wrapper(item, response):
            return await self._process_manim_response(item, response, project_hash, project_dir)

        # 执行批处理（启用深度思考，使用temperature=0.0以获得确定性输出）
        results = await self.batch_processor.process_batch(
            items=batch_items,
            create_messages_func=self._create_messages_for_manim,
            process_response_func=process_manim_response_wrapper,
            max_retries=5,
            temperature=0.0
        )

        return results

    def _create_messages_for_manim(self, item: Tuple[str, str]) -> List[BaseMessage]:
        """
        创建Manim代码生成的消息

        Args:
            item: 包含场景ID和提示词的元组

        Returns:
            List[BaseMessage]: 消息列表
        """
        scene_id, animation_prompt = item
        print(f"准备处理场景: {scene_id}")
        return self.llm.create_messages(
            system_prompt="""
                # Manim动画代码生成AI提示词

                你是一个专业的Manim动画代码生成专家。你的任务是根据提供的动画设计文档，生成完整的Manim社区版Python代码。

                ## 输入要求
                用户将提供一个结构化的动画设计文档，包含以下核心信息：
                - 动画总时长和核心内容
                - 画布尺寸（通常为1920×1080像素，16:9比例）
                - 人物、道具、背景等元素列表及其资产路径
                - 文字关键词元素（内容、位置、样式、时间范围）
                - 分镜/场景时间线（音频内容、涉及元素、视觉布局、动画描述）

                ## 输出要求
                生成一个完整的Manim Python类，继承自Scene，包含construct方法实现完整动画。

                ## 核心实现规范

                ### 1. 类结构和素材清单（必须在代码开头）
                ```python
                from manim import *
                import os
                from layout_manager import LayoutManager

                '''
                ========== 动画素材清单 ==========

                ## 图片素材：
                - [素材名称1]: [文件路径] - [用途描述]
                - [素材名称2]: [文件路径] - [用途描述]
                - ...

                ## 文字关键词素材：
                - "[文字内容1]" - [出现时间] - [样式描述]
                - "[文字内容2]" - [出现时间] - [样式描述]
                - ...

                ## 动画总时长：[X]秒
                =====================================
                '''

                class [AnimationName](Scene):
                    def construct(self):
                        # 设置背景色（根据设计文档）
                        self.camera.background_color = [背景颜色]
                ```

                ### 2. 资产路径管理
                - 严格按照设计文档中的`资产路径`定义图片路径
                - 添加文件存在性检查，如果文件不存在则输出警告信息
                - 支持相对路径和绝对路径

                ### 3. 元素创建规范

                #### 图片元素创建
                ```python
                # 根据设计文档的宽高比计算合适尺寸
                image_obj = ImageMobject(image_path)
                # 根据宽高比设置尺寸（如1:1、16:9、3:4等）
                image_obj.height = [合适高度]
                image_obj.width = [根据宽高比计算的宽度]
                image_obj.set_opacity(0)  # 初始透明
                ```

                #### 文字元素创建
                ```python
                text_obj = Text("文字内容",
                               font_size=[字号],
                               color=[颜色],
                               font="SimHei",  # 中文使用SimHei字体
                               weight=BOLD if needed)
                text_obj.set_opacity(0)  # 初始透明
                ```

                ### 4. **必须使用LayoutManager进行布局管理**

                #### 重要：严格遵循LayoutManager方法调用格式

                ```python
                # 计算安全位置（避免重叠）
                safe_pos = LayoutManager.get_safe_position_right_of(left_obj, right_obj, margin=0.5)
                text_obj.move_to(safe_pos)

                # 确保在屏幕边界内
                LayoutManager.ensure_screen_bounds(text_obj)

                # 调试信息输出
                LayoutManager.print_layout_debug(text_obj, "元素名称")
                ```

                #### LayoutManager可用方法：
                - `get_safe_position_right_of(left_object, right_object, margin=0.5)`
                - `get_safe_position_left_of(right_object, left_object, margin=0.5)`
                - `get_safe_position_above(bottom_object, top_object, margin=0.3)`
                - `get_safe_position_below(top_object, bottom_object, margin=0.3)`
                - `check_overlap(obj1, obj2, margin=0.1)`
                - `print_layout_debug(obj, name)`
                - `ensure_screen_bounds(obj, screen_width=14, screen_height=8, margin=0.2)`

                ### 5. 坐标系统
                - 画布坐标系：14个宽度单位 × 8个高度单位
                - 中心点：(0,0)
                - X轴：-7到+7（左负右正）
                - Y轴：-4到+4（下负上正）

                ### 6. 动画时间线实现（严格按照设计文档）
                **重要：视频时长必须与动画设计文档完全一致**

                根据设计文档的分镜时间戳，使用以下结构：
                ```python
                # 第一阶段 (开始时间-结束时间): 阶段描述
                # 具体动画描述
                self.play(
                    [动画1],
                    [动画2],
                    run_time=[持续时间],  # 必须精确匹配设计文档中的时间
                    rate_func=[缓动函数]
                )

                # wait秒数用于控制每个分镜的精确时长，确保与设计文档一致
                self.wait([等待秒数])
                ```

                ### 7. 动画效果类型
                - **淡入淡出**: `set_opacity(1)` / `set_opacity(0)`
                - **移动**: `move_to([位置])`
                - **缩放**: `scale([比例])`
                - **旋转**: `rotate([角度])`
                - **变换**: `Transform()`
                - **打字机效果**: 逐字符显示文字

                ### 8. 缓动函数选择
                - `smooth`: 平滑过渡
                - `rush_into`: 快进慢出
                - `rush_from`: 慢进快出
                - `there_and_back_with_pause`: 来回运动带停顿

                ### 9. 特殊效果实现
                - **分屏效果**: 创建多个小尺寸对象并使用分隔线
                - **背景模糊**: 使用低透明度大尺寸图片作为背景
                - **打字机效果**: 将文字拆分为单个字符逐一显示
                - **高亮强调**: 使用颜色变化和缩放结合

                ## 代码质量要求

                1. **完整性**: 代码必须完整可运行，包含所有必要的import语句
                2. **注释**: 关键部分添加中文注释说明
                3. **错误处理**: 包含文件存在性检查
                4. **布局安全**: 必须使用LayoutManager确保元素不重叠且在屏幕边界内
                5. **时间准确**: 严格按照设计文档的时间线实现动画
                6. **效果还原**: 准确实现设计文档中描述的视觉效果

                ## 输出格式
                直接输出完整的Python代码，无需额外解释，代码应该可以直接保存为.py文件并运行。

                请根据以上规范，将用户提供的动画设计文档转换为完整的Manim动画代码。
            """,
            history=[],
            query=animation_prompt
        )

    async def _process_manim_response(self, item: Tuple[str, str], response: str, project_hash: str, project_dir: str) -> Tuple[str, str, str]:
        """
        处理Manim代码生成响应

        Args:
            item: 包含场景ID和提示词的元组
            response: LLM生成的响应
            project_hash: 项目哈希
            project_dir: 项目目录

        Returns:
            Tuple[str, str, str]: 包含(场景ID, Manim代码, S3路径)的元组
        """
        scene_id, scene_prompt = item
        max_retries = 5
        retry_count = 0

        while retry_count < max_retries:
            try:
                # 提取Manim代码
                manim_code = ManimUtils.extract_manim_code(response)

                # 检查是否成功提取到代码内容
                if not manim_code:
                    print(f"第{retry_count + 1}次尝试：未能提取到代码内容，将重新生成")
                    raise ValueError("代码提取失败，需要重新生成")

                print(f"成功提取Manim代码")

                # 保存Manim文件到本地并上传到S3
                s3_path = self._save_manim_file(scene_id, manim_code, project_hash, project_dir)

                return scene_id, manim_code, s3_path

            except Exception as e:
                retry_count += 1
                if retry_count >= max_retries:
                    print(f"达到最大重试次数({max_retries})，生成失败")
                    raise e
                print(f"第{retry_count}次尝试失败，准备第{retry_count + 1}次重试")
                # 重新生成Manim代码 - 使用异步方法
                messages = self.llm.create_messages(
                    system_prompt="你是一个专门从事Manim动画的AI助手。对用户提供的动画设计文档，生成相应的、完整的、有效的Manim Python代码。",
                    history=[],
                    query=scene_prompt
                )
                # 调用异步API生成响应，设置temperature为0
                result = await self.llm.generate_response(messages, temperature=0.0)
                response = result

    def _save_manim_file(self, scene_id: str, manim_code: str, project_hash: str, project_dir: str) -> str:
        """
        保存Manim文件到本地并上传到S3

        Args:
            scene_id: 场景ID
            manim_code: Manim代码
            project_hash: 项目哈希
            project_dir: 项目目录

        Returns:
            str: S3路径
        """
        # 保存Manim文件到本地
        manim_filename = f"scene_{scene_id}.py"
        local_manim_path = os.path.join(self.manim_output_dir, project_hash, manim_filename)
        with open(local_manim_path, "w", encoding="utf-8") as f:
            f.write(manim_code)
        print(f"已保存场景 {scene_id} 的Manim文件到本地: {local_manim_path}")

        # 构建S3路径
        s3_manim_path = f"{project_dir}/{manim_filename}"

        # 上传Manim文件到S3
        try:
            self.s3_handler.put_object_by_content(s3_manim_path, manim_code)
            print(f"已上传场景 {scene_id} 的Manim文件到S3: {s3_manim_path}")
        except Exception as e:
            print(f"上传Manim文件到S3失败: {e}")

        return s3_manim_path

    def _save_script_file(self, state: Dict[str, Any], project_dir: str, project_hash: str) -> str:
        """
        保存脚本文件到本地和S3

        Args:
            state: 当前状态
            project_dir: 项目目录
            project_hash: 项目哈希

        Returns:
            str: 数据状态路径
        """
        # 保存脚本文件
        data_state_content = state["data"]
        data_state_path = f"{project_dir}/data_state.json"
        local_data_state_path = os.path.join(self.manim_output_dir, project_hash, "data_state.json")

        # 保存脚本到本地
        # 创建目录路径（如果不存在）
        os.makedirs(os.path.dirname(local_data_state_path), exist_ok=True)

        # 直接使用open写入，避免使用file_manager.write_json
        with open(local_data_state_path, "w", encoding="utf-8") as f:
            json.dump(data_state_content, f, ensure_ascii=False, indent=2)

        # 上传脚本到S3
        try:
            self.s3_handler.put_object_by_content(data_state_path, json.dumps(data_state_content, ensure_ascii=False, indent=2), mimetype="application/json")
            print(f"已上传脚本文件到S3: {data_state_path}")
        except Exception as e:
            print(f"上传脚本文件到S3失败: {e}")

        return data_state_path

    def _update_state_with_results(self, state: Dict[str, Any], results: List[Tuple[str, str, str]]) -> List[Tuple[str, str]]:
        """
        更新状态中的Manim文件信息

        Args:
            state: 当前状态
            results: 生成结果列表

        Returns:
            List[Tuple[str, str]]: 场景路径列表，每项包含(场景ID, S3路径)
        """
        # 更新状态
        state["data"]["manim_files"] = state["data"].get("manim_files", {})
        state["data"]["manim_paths"] = state["data"].get("manim_paths", {})

        # 将结果添加到状态中
        scene_paths = []
        for scene_id, manim_code, s3_path in results:
            state["data"]["manim_files"][scene_id] = manim_code
            state["data"]["manim_paths"][scene_id] = s3_path
            scene_paths.append((scene_id, s3_path))

            # 将Manim代码和路径添加到news_report的对应分镜中
            self._update_news_report_with_manim(state, scene_id, manim_code, s3_path)

        return scene_paths

    def _update_news_report_with_manim(self, state: Dict[str, Any], scene_id: str, manim_code: str, s3_path: str) -> None:
        """
        将Manim代码和路径添加到news_report的对应分镜中

        Args:
            state: 当前状态
            scene_id: 场景ID
            manim_code: Manim代码
            s3_path: S3路径
        """
        # 获取news_report
        news_report = state["data"].get("news_report", {})
        sections = news_report.get("sections", [])

        # 解析场景ID
        if "_shot_" in scene_id:
            # 镜头格式: section_X_shot_Y
            parts = scene_id.split('_')
            section_index = int(parts[1]) - 1
            shot_index = int(parts[3]) - 1

            # 确保索引有效
            if 0 <= section_index < len(sections) and "subsections" in sections[section_index]:
                subsections = sections[section_index].get("subsections", [])
                if 0 <= shot_index < len(subsections):
                    # 更新子镜头的manim_code和manim_path
                    sections[section_index]["subsections"][shot_index]["manim_code"] = manim_code
                    sections[section_index]["subsections"][shot_index]["manim_path"] = s3_path
                    print(f"已更新 {scene_id} 的manim_code和manim_path")
        else:
            # 普通场景格式: section_X
            section_index = int(scene_id.split('_')[1]) - 1
            if 0 <= section_index < len(sections):
                # 更新场景的manim_code和manim_path
                sections[section_index]["manim_code"] = manim_code
                sections[section_index]["manim_path"] = s3_path
                print(f"已更新 {scene_id} 的manim_code和manim_path")

        # 更新news_report
        state["data"]["news_report"] = news_report